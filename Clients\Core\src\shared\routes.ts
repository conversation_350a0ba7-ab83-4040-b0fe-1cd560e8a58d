export const ROUTE_WILDCARD = "*";
export const ROUTE_ROOT = "/";
export const ROUTE_AUTH = "/auth";
export const ROUTE_IDENTITYPROVIDER_CALLBACK =
    "/idpcallback/:idpid/:accesstoken/:refreshtoken/:expiresin";
export const ROUTE_IDENTITYPROVIDER_PUBLISHER_CALLBACK =
    "/idpcallback/:idpid/:originatinghubid/:accesstoken/:refreshtoken/:expiresin";
export const ROUTE_ACCOUNTSETUP = "/accountsetup";
export const ROUTE_ASSIGNMENTS_EDIT_STATUS = "/assignments/editstatus";
export const ROUTE_ASSIGN_AUDIT = "/assignments/assignmentaudit";
export const ROUTE_ASSIGN_USERINSIGHTS = "/assignuserinsights";
export const ROUTE_CART = "/cart";
export const ROUTE_USERGROUP = "/usergroup";
export const ROUTE_ADVANCEDFILTER = "/advancedfilter";
export const ROUTE_PUBLISHEDCONTENTSOURCECONSENTCOMPLETED =
    "/publishedContenSourceConsentCompleted";
export const ROUTE_CONNECTORAUTH = "/connectorAuth";
export const ROUTE_CONNECTORAUTHDEBUG = "/connectorAuthDebug";
export const ROUTE_CONNECTORAUTHCOMPLETE = "/connectorAuthComplete";
export const ROUTE_CONNECTORAUTHSIGNOUT = "/connectorAuthSignout";
export const ROUTE_CONNECTORAUTHSIGNOUTCOMPLETE = "/connectorAuthSignoutComplete";
export const ROUTE_CONNECTSOURCERESTRICTIONS = "/contentsourcerestrictedactions";
export const ROUTE_SIGNOUT = "/signout";
export const ROUTE_SIGNOUT_COMPLETE = "/signoutComplete";
export const ROUTE_CREATE_SMART_LAYOUT = "/create/smartlayout";
export const ROUTE_CREATE_OUTPUTSETTINGS = "/create/outputsettings";
export const ROUTE_CREATE_SMARTFIELDS = "/create/smartfields";
export const ROUTE_CREATE_COMPLETED = "/create/complete";
export const ROUTE_CREATE_COMPLETED_ID = "/create/complete/:mergeId";
export const ROUTE_CREATE_FAILED = "/create/failed";
export const ROUTE_CREATE_CONTENT_SOURCE_AUTHENTICATION = "/create/contentsourceauthentication";
export const ROUTE_ERROR = "/error";
export const ROUTE_FAVORITE_SEARCH_EDITOR = "/favoritesearch";
export const ROUTE_FIELDSYNCMODAL = "/fieldsyncmodal";
export const ROUTE_HOME = "/home";
export const ROUTE_HOME_ASSIGNMENTS = "/home/<USER>";
export const ROUTE_HOME_CUSTOMPAGE = "/home/<USER>";
export const ROUTE_HOME_CUSTOMPAGE_ID = "/home/<USER>/:id";
export const ROUTE_HOME_CACHEMANAGER = "/home/<USER>";
export const ROUTE_HOME_IDENTITYPROVIDERS = "/home/<USER>";
export const ROUTE_HOME_TOKENMANAGER = "/home/<USER>";
export const ROUTE_HOME_CLIENTAPPLICATIONS = "/home/<USER>";
export const ROUTE_HOME_ASSIGN = "/home/<USER>";
export const ROUTE_HOME_CLIP = "/home/<USER>";
export const ROUTE_HOME_CURRENTPURSUIT = "/home/<USER>";
export const ROUTE_HOME_DASHBOARD = "/home/<USER>";
export const ROUTE_HOME_DASHBOARD_SEARCH = "/home/<USER>/search";
export const ROUTE_HOME_DASHBOARD_CAMPAIGN = "/home/<USER>/campaign";
export const ROUTE_HOME_DASHBOARD_HOME = "/home/<USER>/home";
export const ROUTE_HOME_DASHBOARD_SIGNUPINFO = "/home/<USER>/signupinfo";
export const ROUTE_HOME_DASHBOARD_FAVORITES = "/home/<USER>/favorites";
export const ROUTE_HOME_DASHBOARD_FAVORITELOCATIONS = "/home/<USER>/favoritelocations";
export const ROUTE_HOME_DASHBOARD_FAVORITESEARCHES = "/home/<USER>/favoritesearches";
export const ROUTE_HOME_DASHBOARD_RECENTLYUSED = "/home/<USER>/recentlyused";
export const ROUTE_HOME_DASHBOARD_FREQUENTLYUSED = "/home/<USER>/frequentlyused";
export const ROUTE_HOME_DASHBOARD_RECENTLYCLIPPED = "/home/<USER>/recentlyclipped";
export const ROUTE_HOME_DASHBOARD_TODOS = "/home/<USER>/todos";
export const ROUTE_HOME_DASHBOARD_CAMPAIGNS = "/home/<USER>/campaigns";
export const ROUTE_HOME_DASHBOARD_CAMPAIGNS_FEATURED = "/home/<USER>/campaigns/featured";
export const ROUTE_HOME_QPILOT = "/home/<USER>";
export const ROUTE_HOME_DESIGN = "/home/<USER>";
export const ROUTE_HOME_FINALIZE = "/home/<USER>";
export const ROUTE_HOME_MYSETTINGS = "/home/<USER>";
export const ROUTE_HOME_QNAMAKER = "/home/<USER>";
export const ROUTE_HOME_QPILOT_INDEXES = "/home/<USER>";
export const ROUTE_HOME_CONTENTSOURCE_CONFIG_MIGRATION = "/home/<USER>";
export const ROUTE_HOME_QPILOT_INDEX_RUNS = "/home/<USER>/:id";
export const ROUTE_HOME_ANSWER = "/home/<USER>";
export const ROUTE_HOME_ANSWER_NOT_CONFIGURED = "/home/<USER>/notconfigured";
export const ROUTE_HOME_ANSWER_QUERY = "/home/<USER>/query";
export const ROUTE_ANSWERSMARTFIELDS = "/answersmartfields";
export const ROUTE_RANGESELECTION = "/rangeselection";
export const ROUTE_HOME_LICENSE_MATRIX = "/home/<USER>/matrix";
export const ROUTE_HOME_MANAGE_CREATEHUB = "/home/<USER>/createhub";
export const ROUTE_HOME_MANAGE_CONTENTSOURCES = "/home/<USER>/contentsources";
export const ROUTE_HOME_MANAGE_CONTENTSOURCES_STATUS = "/home/<USER>/contentsources/:status";
export const ROUTE_HOME_MANAGE_CONTENTSOURCES_EDITINSTANCE =
    "/home/<USER>/contentsources/editinstance";
export const ROUTE_HOME_MANAGE_CONTENTSOURCES_EDITINSTANCE_CONNECTORID_INSTANCEID =
    "/home/<USER>/contentsources/editinstance/:connectorId/:instanceId";
export const ROUTE_HOME_MANAGE_CONTENTSOURCES_EDITINSTANCE_CONNECTORID =
    "/home/<USER>/contentsources/editinstance/:connectorId";
export const ROUTE_HOME_MANAGE_HUBS_PURCHASE = "/home/<USER>/hubs/purchase/:hubId";
export const ROUTE_HOME_MANAGE_HUBS_PURCHASE_HUBID = "/home/<USER>/hubs/purchase/:hubId";
export const ROUTE_HOME_MANAGE_HUBS = "/home/<USER>/hubs";
export const ROUTE_HOME_MANAGE_HUBDETAILS = "/home/<USER>/hubdetails";
export const ROUTE_HOME_MANAGE_HUBDETAILS_HUBID = "/home/<USER>/hubdetails/:hubId";
export const ROUTE_HOME_MANAGE_PUBLISHERPROVISIONING = "/home/<USER>/publisherprovisioning";
export const ROUTE_HOME_MANAGE_PUBLISHERPROVISIONING_CONFIGURE =
    "/home/<USER>/publisherprovisioning/configure";
export const ROUTE_HOME_REFRESH = "/home/<USER>";
export const ROUTE_HOME_SCHEDULEDTASKS = "/home/<USER>";
export const ROUTE_HOME_SETTINGS = "/home/<USER>";
export const ROUTE_HOME_SETTINGS_HUBMANAGEMENT = "/home/<USER>/hubmanagement";
export const ROUTE_HOME_SETTINGS_USERMANAGEMENT = "/home/<USER>/usermanagement";
export const ROUTE_HOME_SETTINGS_USERMANAGEMENT_EDITUSER = "/home/<USER>/usermanagement/edituser";
export const ROUTE_HOME_SETTINGS_USERMANAGEMENT_EDITUSER_USERID =
    "/home/<USER>/usermanagement/edituser/:userId";
export const ROUTE_HOME_SETTINGS_USERMANAGEMENT_ADDUSER = "/home/<USER>/usermanagement/adduser";
export const ROUTE_HOME_SETTINGS_USERMANAGEMENT_USERGROUP =
    "/home/<USER>/usermanagement/usergroup";
export const ROUTE_HOME_SHAREDDOCUMENTS = "/home/<USER>";
export const ROUTE_HOME_SUSPENDED = "/home/<USER>";
export const ROUTE_HOME_TRIALEXPIRED = "/home/<USER>";
export const ROUTE_HOME_TRIALEXPIREDSUSPENED = "/home/<USER>";
export const ROUTE_HOME_USERPROFILE = "/home/<USER>";
export const ROUTE_HOME_USERLOOKUP_HUBS = "/home/<USER>/hubs";
export const ROUTE_HOME_WELCOME = "/home/<USER>";
export const ROUTE_IDTOKEN = "/id_token*";
export const ROUTE_INFO = "/info";
export const ROUTE_INFO_PREVIEW_PAGE = "/info/preview/page";
export const ROUTE_INFO_PREVIEW_GALLERY = "/info/preview/gallery";
export const ROUTE_INFO_PREVIEW_WEB = "/info/preview/web";
export const ROUTE_INFO_PROPERTIES = "/info/properties";
export const ROUTE_INFO_USAGE = "/info/usage";
export const ROUTE_INFO_USERS = "/info/users";
export const ROUTE_INFO_SEARCHTERMS = "/info/searchterms";
export const ROUTE_INFO_DESTINATIONS = "/info/destinations";
export const ROUTE_INFO_VIEWS = "/info/views";
export const ROUTE_INFO_COMMENTS = "/info/comments";
export const ROUTE_INFO_INSIGHTS = "/info/insights";
export const ROUTE_INFO_PURSUITS = "/info/pursuits";
export const ROUTE_JOINHUB = "/joinhub";
export const ROUTE_LOGIN = "/login";
export const ROUTE_CUSTOMLOGIN = "/customlogin";
export const ROUTE_UNKNOWNUSERLOGIN = "/unknownuserlogin";
export const ROUTE_LOADER = "/loader";
export const ROUTE_PUBLISHERSIGNUP = "/publisherSignup";
export const ROUTE_PUBLISHERSIGNUP_PUBLISHERCONTENTSOURCEID =
    "/publisherSignup/:publisherContentSourceId";
export const ROUTE_REDIR = "/redir";
export const ROUTE_RULESYNCMODAL = "/rulesyncmodal";
export const ROUTE_SIGNUP = "/signup";
export const ROUTE_SHAREDOCUMENT = "/shareDocument";
export const ROUTE_GETALINK = "/getALink";
export const ROUTE_SUBSCRIBE = "/subscribe";
export const ROUTE_SUBSCRIBE_AUTHCOMPLETE = "/subscribe/authComplete";
export const ROUTE_SUBSCRIBE_IDP = "/subscribe/idp";
export const ROUTE_SUBSCRIBE_IDP_IDPID = "/subscribe/idp/:idpid";
export const ROUTE_SUBSCRIBE_HUBID_HUBNAME = "/subscribe/:hubId/:hubName";
export const ROUTE_SMARTFIELDEDITOR = "/smartfieldeditor";
export const ROUTE_SMARTFIELDSORDERDETAILS = "/smartfieldsorderdetails";
export const ROUTE_SMARTRULES = "/smartrules";
export const ROUTE_TRACKEDINSIGHTS = "/trackedinsights";
export const ROUTE_DATAGENERATOR = "/generatedata";
export const ROUTE_HOME_MANAGE_PURSUITTYPES = "/home/<USER>/pursuittypes";
export const ROUTE_HOME_MANAGE_EDITPURSUITTYPE_ID = "/home/<USER>/editpursuittype/:id";
export const ROUTE_HOME_MANAGE_EDITPURSUITTYPE = "/home/<USER>/editpursuittype";
export const ROUTE_HOME_MANAGE_PURSUIT = "/home/<USER>/pursuit";
export const ROUTE_HOME_MANAGE_PURSUIT_ID_ASSIGNMENT_ID_COMMENTS =
    "/home/<USER>/pursuit/:id/:documentView/:selectedTab/:assignmentId/comments";
export const ROUTE_HOME_MANAGE_PURSUIT_ID_DOCUMENTVIEW =
    "/home/<USER>/pursuit/:id/:documentView/:selectedTab";
export const ROUTE_HOME_MANAGE_PURSUITS = "/home/<USER>/pursuits";
export const ROUTE_HOME_MANAGE_PURSUIT_ID = "/home/<USER>/pursuit/:id";
export const ROUTE_HOME_MANAGE_PURSUITS_ID_TITLE = "/home/<USER>/pursuits/:id/:title";
export const ROUTE_HOME_MANAGE_EDITPURSUIT_ID_SMMARTFIELDS =
    "/home/<USER>/:id/:documentView/:tab";
export const ROUTE_HOME_MANAGE_EDITPURSUIT_ID = "/home/<USER>/:id";
export const ROUTE_HOME_MANAGE_EDITPURSUIT = "/home/<USER>";
export const ROUTE_HOME_MANAGE_CREATEPURSUIT = "/home/<USER>";
export const ROUTE_HOME_MANAGE_CREATEPURSUIT_ID = "/home/<USER>/:pursuitTypeId";
export const ROUTE_PURSUITCREATELINK = "/createpursuit/:pursuitTypeId";
export const ROUTE_HOME_MANAGE_SMARTFIELDS = "/home/<USER>/smartfields";
export const ROUTE_HOME_MANAGE_CONTENTSOURCES_CHANGEMANAGEMENT =
    "/home/<USER>/contentsources/changemanagement/:contentSourceId/:tabPath";
export const ROUTE_HOME_MAINTENANCE = "/home/<USER>";
export const ROUTE_HOME_PURSUIT_ASSIGNMENTS = "/home/<USER>/assignments";
export const ROUTE_PURSUIT_RECOMMENDCONTENT = "/pursuit/recommendcontent";
export const ROUTE_HOME_PURSUIT_SHARES = "/home/<USER>/shares";
export const ROUTE_COPYTO = "/copyto";
export const ROUTE_COPYTO_PURSUIT = "/copyto/pursuit";
export const ROUTE_COPYTO_LOCATION = "/copyto/location";
export const ROUTE_MOVETO = "/moveto";
export const ROUTE_DOC_CONTENTSOURCEID_FILEID = "/doc/:contentSourceId/:fileId";
export const ROUTE_DOC_CONTENTSOURCEID_FILEID_IDENTITYPROVIDER =
    "/doc/:contentSourceId/:fileId/:identityProvider";
export const ROUTE_PURSUITTYPE_CUSTOMTABEDITOR = "/editcustomtab";
export const ROUTE_PURSUITGETALINKMODAL = "/pursuitgetalinkmodal";
export const ROUTE_PURSUITCHANGESTATUSMODAL = "/pursuitchangestatusmodal";
export const ROUTE_CAMPAIGNGETALINKMODAL = "/campaigngetalinkmodal";
export const ROUTE_CAMPAIGN_LINK_REDEEM = "/campaignlink/:campaignId";
export const ROUTE_CAMPAIGN_LINK_REDEEM_IDENTITYPROVIDER =
    "/campaignlink/:campaignId/:identityProvider";
export const ROUTE_PURSUIT_PURSUITID = "/pursuit/:pursuitId";
export const ROUTE_PURSUIT_PURSUITID_IDENTITYPROVIDER = "/pursuit/:pursuitId/:identityProvider";
export const ROUTE_FEATURED_CAMPAIGNS = "/featuredcampaigns";
export const ROUTE_CUSTOMHOMEPAGE = "/customhomepage";
export const ROUTE_CONTENTSOURCES_CONTENTSOURCEID_MANAGEAUTHENTICATION =
    "/contentsources/:contentSourceId/manageauthentication";
export const ROUTE_DOCUMENTSHAREVIEW_SHAREID = "/viewer/:shareId";
export const ROUTE_DOCUMENTSHAREVIEW_SHAREID_VALIDATE = "/viewer/:shareId/:validate";
export const ROUTE_HOME_MANAGE_CONTENTSOURCES_EXTERNAL_URL =
    "/home/<USER>/contentsources/external/:url";
export const ROUTE_UPLOADFILES = "/uploadfiles";
export const ROUTE_ASSOCIATEPURSUIT = "/associatepursuit";
export const ROUTE_WILDCARD_RELOAD = "/*/reload";
export const ROUTE_HOME_MANAGE_SMARTLISTS = "/home/<USER>/smartlists";
export const ROUTE_HOME_MANAGE_EDITSMARTLISTS_ID = "/home/<USER>/editsmartlists/:id";
export const ROUTE_HOME_MANAGE_EDITSMARTLISTS = "/home/<USER>/editsmartlists";
export const ROUTE_HOME_MANAGE_ADDSMARTLISTS = "/home/<USER>/addsmartlists";

export const ROUTE_GENERATIVEAI_TESTBENCH = "/home/<USER>";
export const ROUTE_GENERATIVEAI_SEARCHTESTBENCH = "/home/<USER>";

export const ROUTE_GENERATIVEAI_ADDIN_AGENT = "/ai/agent";
export const ROUTE_GENERATIVEAI_ADDIN_AGENT_TASKPANE = "/home/<USER>/agent";

export const ROUTE_HOME_MANAGE_SMARTSKILLS = "/home/<USER>/smartskills";
export const ROUTE_HOME_MANAGE_EDITSMARTSKILL_ID = "/home/<USER>/editsmartskill/:id";
export const ROUTE_HOME_MANAGE_EDITSMARTSKILL = "/home/<USER>/editsmartskill";
export const ROUTE_HOME_MANAGE_ADDSMARTSKILL = "/home/<USER>/addsmartskill";
export const ROUTE_MERGEPROFILE = "/mergeprofile";
export const ROUTE_MERGEPROFILE_ID = "/mergeprofile/:id";
