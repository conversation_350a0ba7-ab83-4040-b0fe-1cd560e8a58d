import * as React from "react";
import { connect } from "react-redux";
import { Switch, Route, with<PERSON><PERSON><PERSON>, RouteComponentProps } from "react-router";
import * as Types from "src/controlpanel/types";
import * as SharedTypes from "src/shared/types";
import { LeftNav } from "src/shared/components/leftnav/leftnav";
import { TitleBar } from "src/shared/components/titlebar/titlebar";
import Hubs from "src/controlpanel/components/hubs/hubs";
import { decodeJwt } from "src/shared/utilities/jwt";
import Store from "src/controlpanel/store";
import * as Actions from "src/controlpanel/actioncreators";
import * as Styles from "./layout.css";
import HubDetails from "src/controlpanel/components/hubdetails/hubdetails";
import Purchase from "src/controlpanel/components/purchase/purchase";
import CreateHub from "src/controlpanel/components/createhub/createhub";
import { Dashboard } from "src/controlpanel/components/dashboard/dashboard";
import ScheduledTasks from "src/controlpanel/components/scheduledtasks/scheduledtasks";
import LicenseMatrix from "src/controlpanel/components/license/components/licensematrix/licensematrix";
import CacheManager from "src/controlpanel/components/cachemanager/cachemanager";
import ClientApplications from "src/controlpanel/components/clientapplications/clientapplications";
import UserLookup from "src/controlpanel/components/userlookup/userlookup";
import QnAMaker from "src/controlpanel/components/qnamaker/qnamaker";
import Settings from "src/controlpanel/components/settings/settings";
import { TokenManager } from "src/controlpanel/components/tokenmanager/tokenmanager";
import {
    ROUTE_HOME_MANAGE_HUBS_PURCHASE_HUBID,
    ROUTE_HOME_MANAGE_HUBS,
    ROUTE_HOME_DASHBOARD,
    ROUTE_HOME_MANAGE_HUBDETAILS_HUBID,
    ROUTE_HOME_MANAGE_CREATEHUB,
    ROUTE_HOME_USERLOOKUP_HUBS,
    ROUTE_HOME_LICENSE_MATRIX,
    ROUTE_HOME_SCHEDULEDTASKS,
    ROUTE_HOME_CACHEMANAGER,
    ROUTE_HOME_CLIENTAPPLICATIONS,
    ROUTE_WILDCARD,
    ROUTE_LOGIN,
    ROUTE_HOME_SETTINGS,
    ROUTE_HOME_QNAMAKER,
    ROUTE_HOME_IDENTITYPROVIDERS,
    ROUTE_HOME_TOKENMANAGER,
    ROUTE_HOME_QPILOT_INDEXES,
    ROUTE_HOME_QPILOT_INDEX_RUNS,
    ROUTE_HOME_CONTENTSOURCE_CONFIG_MIGRATION
} from "src/shared/routes";
import TopNav from "src/shared/components/topnav/topnav";
import { IdentityProviders } from "src/controlpanel/components/identityproviders/identityproviders";
import { QPilotIndexes } from "../qpilotindexes/qpilotindexes";
import { QPilotIndexRuns } from "../qpilotindexes/qpilotindexruns/qpilotindexruns";
import { ContentSourceConfigMigration } from "../contentsourceconfigmigration/contentsourceconfigmigration";

interface LayoutPropeties extends RouteComponentProps {
    allMenuItems?: SharedTypes.LeftNavMenuGroup[];
    idToken?: string;
    clientId?: string;
    hubId?: number;
    currentUserName?: string;
    userLogin?: string;
    pageTitle?: string;
    tenant?: string;
    selectedMenuItem?: string;
}

class Layout extends React.Component<LayoutPropeties, undefined> {
    private menuGroupClickHandler = (path: string): void => {
        window.location.hash = path;
    };

    private menuItemClickHandler = (path: string): void => {
        Store.dispatch(Actions.setSelectedMenuItem(path));
        window.location.hash = path;
    };

    private menuGroupExpandCollapse = (name: string, isExpanded: boolean): void => {
        const modifiedMenuItems = this.props.allMenuItems.slice();
        const affectedMenuGroup = modifiedMenuItems.find(m => {
            if (m.name === name) {
                return true;
            }
            return false;
        });
        affectedMenuGroup.isExpanded = isExpanded;
        Store.dispatch(Actions.setMenuItems(modifiedMenuItems));
    };

    public componentDidMount(): void {
        if (!this.props.idToken || this.props.idToken === "null") {
            window.location.hash = ROUTE_LOGIN;
            return;
        }
        const decodedToken = decodeJwt(this.props.idToken);
        const parsedToken = JSON.parse(decodedToken);

        Store.dispatch(Actions.setCurrentUsername(parsedToken.name));
        Store.dispatch(Actions.setUserLogin(parsedToken.email));
    }

    public render(): JSX.Element {
        return (
            <div className={Styles.content}>
                <TopNav
                    title="Control Panel"
                    currentUserName={this.props.currentUserName}
                    userLogin={this.props.userLogin}
                    showHelp={false}
                    showUserProfileSettings={false}
                    downloadProfileImage={false}
                    showInstallAddins={false}
                />
                <div className={Styles.layoutContainer}>
                    {this.props.allMenuItems && (
                        <LeftNav
                            items={this.props.allMenuItems}
                            onMenuGroupClick={this.menuGroupClickHandler}
                            onMenuItemClick={this.menuItemClickHandler}
                            onMenuGroupExpandCollapse={this.menuGroupExpandCollapse}
                            selectedMenuItem={this.props.selectedMenuItem}
                        />
                    )}
                    <div className={Styles.body}>
                        <TitleBar pageTitle={this.props.pageTitle} className={Styles.titleBar} />
                        <div className={Styles.pageContent}>
                            <Switch>
                                <Route
                                    path={ROUTE_HOME_MANAGE_HUBS_PURCHASE_HUBID}
                                    component={Purchase}
                                />
                                <Route path={ROUTE_HOME_MANAGE_HUBS} component={Hubs} />
                                <Route path={ROUTE_HOME_DASHBOARD} component={Dashboard} />
                                <Route
                                    path={ROUTE_HOME_MANAGE_HUBDETAILS_HUBID}
                                    component={HubDetails}
                                />
                                <Route path={ROUTE_HOME_MANAGE_CREATEHUB} component={CreateHub} />
                                <Route path={ROUTE_HOME_USERLOOKUP_HUBS} component={UserLookup} />
                                <Route path={ROUTE_HOME_LICENSE_MATRIX} component={LicenseMatrix} />
                                <Route
                                    path={ROUTE_HOME_SCHEDULEDTASKS}
                                    component={ScheduledTasks}
                                />
                                <Route path={ROUTE_HOME_CACHEMANAGER} component={CacheManager} />
                                <Route
                                    path={ROUTE_HOME_IDENTITYPROVIDERS}
                                    component={IdentityProviders}
                                />
                                <Route
                                    path={ROUTE_HOME_CLIENTAPPLICATIONS}
                                    component={ClientApplications}
                                />
                                <Route path={ROUTE_HOME_TOKENMANAGER} component={TokenManager} />
                                <Route path={ROUTE_HOME_SETTINGS} component={Settings} />
                                <Route path={ROUTE_HOME_QNAMAKER} component={QnAMaker} />
                                <Route path={ROUTE_HOME_QPILOT_INDEXES} component={QPilotIndexes} />
                                <Route
                                    path={ROUTE_HOME_CONTENTSOURCE_CONFIG_MIGRATION}
                                    component={ContentSourceConfigMigration}
                                />
                                <Route
                                    path={ROUTE_HOME_QPILOT_INDEX_RUNS}
                                    component={QPilotIndexRuns}
                                />
                                <Route path={ROUTE_WILDCARD} component={Hubs} />
                            </Switch>
                        </div>
                    </div>
                </div>
            </div>
        );
    }
}

const mapStateToProps = (store: Types.Store): Partial<LayoutPropeties> => ({
    idToken: store.controlPanel.idToken,
    allMenuItems: store.app.menuItems,
    pageTitle: store.app.pageTitle,
    currentUserName: store.app.currentUserName,
    userLogin: store.app.userLogin,
    tenant: store.app.tenant,
    selectedMenuItem: store.app.selectedMenuItem
});

export default withRouter<LayoutPropeties, null>(
    connect<unknown, unknown, LayoutPropeties>(mapStateToProps, null)(Layout)
);
