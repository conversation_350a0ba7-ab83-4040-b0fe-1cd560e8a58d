import { Api } from "./api";

export interface ContentSourceConfigProgressDto {
    total: number;
    currentCount: number;
    stepTotal: number;
    currentAction: string;
    stepCount: number;
    debugText: string;
    IsInProgress: boolean;
}

export interface ContentSourceBackupConfigDto {
    contentSourceId: string;
    createOn: Date;
    hubId: number;
    hubName: string;
    message: string;
    contentSourceName: string;
}

export class ContentSourceApi extends Api {
    public publishContentSource(contentSourceId: any): Promise<void> {
        return this.post<any, any>(`contentsource/${contentSourceId}/publish`, {});
    }

    // stopSharingContentSource
    public unpublishContentSource(contentSourceId: any, hubid: number): Promise<void> {
        return this.post<any, any>(`contentSource/${contentSourceId}/${hubid}/unpublish`, {});
    }

    public migrateAllFiltersOutOfConfigJson(): Promise<boolean> {
        return this.post<any, boolean>("contentSource/ContentSourceConfigs/Migrate", {});
    }

    public getContentSourceMigrationProgress(): Promise<ContentSourceConfigProgressDto> {
        return this.get<ContentSourceConfigProgressDto>(
            "contentSource/ContentSourceConfigs/Progress"
        );
    }

    public getContentSourceMigrationBackups(): Promise<ContentSourceBackupConfigDto[]> {
        return this.get<ContentSourceBackupConfigDto[]>(
            "contentSource/ContentSourceConfigs/Backups"
        );
    }

    public cancelContentSourceMigration(): Promise<boolean> {
        return this.post<any, boolean>("contentSource/ContentSourceConfigs/Migrate/Cancel", {});
    }
}
