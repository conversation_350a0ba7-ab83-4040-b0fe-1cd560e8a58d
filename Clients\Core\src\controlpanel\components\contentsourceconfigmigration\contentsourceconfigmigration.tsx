import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { AnyAction, Dispatch } from "redux";
import {
    ClickButton,
    ContentBox,
    DataTable,
    DataTableColumn,
    ToolBarButtonProperties
} from "src/cl";
import {
    ContentSourceApi,
    ContentSourceConfigProgressDto,
    ContentSourceBackupConfigDto
} from "src/controlpanel/api/contentsource";
import { Store } from "src/shared/types";
import * as Actions from "src/controlpanel/actioncreators";
import { dateFormat } from "src/cl/components/datatable/datatablecolumnformat";

export const ContentSourceConfigMigration: React.FunctionComponent = () => {
    const [isLoading, setIsLoading] = React.useState<boolean>(false);
    const [isMigrating, setIsMigrating] = React.useState<boolean>(false);
    const [progress, setProgress] = React.useState<ContentSourceConfigProgressDto | null>(null);
    const [backups, setBackups] = React.useState<ContentSourceBackupConfigDto[]>([]);
    const [progressInterval, setProgressInterval] = React.useState<NodeJS.Timeout | null>(null);

    const idToken = useSelector<Store, string>(store => store.controlPanel.idToken);

    const dispatch: Dispatch<AnyAction> = useDispatch();

    React.useEffect(() => {
        dispatch(Actions.setPageTitle("Content Source Config Migration"));
    }, []);

    const getBackups = async () => {
        try {
            setIsLoading(true);
            const api = new ContentSourceApi(idToken);
            setBackups(await api.getContentSourceMigrationBackups());
        } finally {
            setIsLoading(false);
        }
    };

    const getProgress = async () => {
        try {
            const api = new ContentSourceApi(idToken);
            const progressData = await api.getContentSourceMigrationProgress();
            setProgress(progressData);

            // Stop polling if migration is complete
            if (progressData.currentCount >= progressData.total && progressData.total > 0) {
                if (progressInterval) {
                    clearInterval(progressInterval);
                    setProgressInterval(null);
                }
                setIsMigrating(false);
                // Refresh backups when migration is complete
                await getBackups();
            }
        } catch (error) {
            console.error("Error getting migration progress:", error);
        }
    };

    const startMigration = async () => {
        try {
            setIsMigrating(true);
            const api = new ContentSourceApi(idToken);
            void api.migrateAllFiltersOutOfConfigJson();

            // Start polling for progress every second
            const interval = setInterval(getProgress, 1000);
            setProgressInterval(interval);

            // Get initial progress
            await new Promise(resolve => setTimeout(resolve, 1000));
            await getProgress();
        } catch (error) {
            console.error("Error starting migration:", error);
            setIsMigrating(false);
        }
    };

    const cancelMigration = async () => {
        try {
            const api = new ContentSourceApi(idToken);
            await api.cancelContentSourceMigration();

            // Stop polling
            if (progressInterval) {
                clearInterval(progressInterval);
                setProgressInterval(null);
            }

            setIsMigrating(false);
            setProgress(null);

            // Refresh backups to show any partial results
            await getBackups();
        } catch (error) {
            console.error("Error canceling migration:", error);
        }
    };

    useEffect(() => {
        void getBackups();

        // Cleanup interval on unmount
        return () => {
            if (progressInterval) {
                clearInterval(progressInterval);
            }
        };
    }, []);

    const formatErrorMessage = (rowData: ContentSourceBackupConfigDto) => {
        if (!rowData.message) {
            return <div>-</div>;
        }
        return (
            <div title={rowData.message}>
                {rowData.message.length > 50
                    ? `${rowData.message.substring(0, 50)}...`
                    : rowData.message}
            </div>
        );
    };

    const columns: DataTableColumn[] = [
        {
            headerText: "Content Source Name",
            fieldName: "contentSourceName",
            sortable: true,
            width: "250"
        },
        {
            headerText: "Hub ID",
            fieldName: "hubId",
            sortable: true,
            width: "100"
        },
        {
            headerText: "Hub Name",
            fieldName: "hubName",
            sortable: true,
            width: "200"
        },
        {
            headerText: "Created On",
            fieldName: "createOn",
            sortable: true,
            format: dateFormat,
            width: "150"
        },
        {
            headerText: "Message",
            fieldName: "message",
            sortable: true,
            format: formatErrorMessage,
            width: "300"
        }
    ];

    const buttons: ToolBarButtonProperties[] = [];

    buttons.push({
        text: "Refresh",
        type: ClickButton,
        onClick: getBackups
    });

    const overallProgressPercentage =
        progress && progress.total > 0
            ? Math.round((progress.currentCount / progress.total) * 100)
            : 0;

    const stepProgressPercentage =
        progress && progress.stepTotal > 0
            ? Math.round((progress.stepCount / progress.stepTotal) * 100)
            : 0;

    return (
        <ContentBox title="" showLoader={false} showTitle={false}>
            <div style={{ padding: "20px" }}>
                {/* Migration Control Section */}
                <div
                    style={{
                        marginBottom: "30px",
                        padding: "20px",
                        border: "1px solid #ddd",
                        borderRadius: "4px",
                        backgroundColor: "#f9f9f9"
                    }}>
                    <h3 style={{ marginTop: "0", marginBottom: "20px" }}>Migration Control</h3>

                    <div style={{ marginBottom: "20px", display: "flex", gap: "10px" }}>
                        <button
                            onClick={startMigration}
                            disabled={isMigrating}
                            style={{
                                padding: "10px 20px",
                                backgroundColor: isMigrating ? "#ccc" : "#007bff",
                                color: "white",
                                border: "none",
                                borderRadius: "4px",
                                cursor: isMigrating ? "not-allowed" : "pointer",
                                fontSize: "14px"
                            }}>
                            {isMigrating ? "Migration in Progress..." : "Start Migration"}
                        </button>

                        {isMigrating && (
                            <button
                                onClick={cancelMigration}
                                style={{
                                    padding: "10px 20px",
                                    backgroundColor: "#dc3545",
                                    color: "white",
                                    border: "none",
                                    borderRadius: "4px",
                                    cursor: "pointer",
                                    fontSize: "14px"
                                }}>
                                Cancel Migration
                            </button>
                        )}
                    </div>

                    {/* Progress Display */}
                    {progress && (
                        <div style={{ marginTop: "20px" }}>
                            <div style={{ marginBottom: "15px" }}>
                                <strong>Migration Progress:</strong>
                            </div>
                            {/* Current Action */}
                            {progress.currentAction && (
                                <>
                                    <div style={{ marginBottom: "15px", fontSize: "14px" }}>
                                        <span>Current Action: </span>
                                        <strong style={{ color: "#007bff" }}>
                                            {progress.currentAction}
                                        </strong>
                                    </div>
                                    <div style={{ marginBottom: "15px", fontSize: "14px" }}>
                                        <span>Current Action: </span>
                                        <strong style={{ color: "#007bff" }}>
                                            {progress.debugText}
                                        </strong>
                                    </div>
                                </>
                            )}

                            {/* Overall Progress */}
                            <div style={{ marginBottom: "20px" }}>
                                <div style={{ marginBottom: "8px" }}>
                                    <span style={{ fontSize: "14px", fontWeight: "600" }}>
                                        Overall Progress:
                                    </span>
                                    <span style={{ float: "right", fontSize: "14px" }}>
                                        {progress.currentCount} / {progress.total} (
                                        {overallProgressPercentage}%)
                                    </span>
                                </div>
                                <div
                                    style={{
                                        width: "100%",
                                        backgroundColor: "#e0e0e0",
                                        borderRadius: "4px",
                                        overflow: "hidden",
                                        height: "20px"
                                    }}>
                                    <div
                                        style={{
                                            width: `${overallProgressPercentage}%`,
                                            height: "100%",
                                            backgroundColor: "#007bff",
                                            transition: "width 0.3s ease"
                                        }}
                                    />
                                </div>
                            </div>

                            {/* Step Progress */}
                            <div style={{ marginBottom: "10px" }}>
                                <div style={{ marginBottom: "8px" }}>
                                    <span style={{ fontSize: "14px", fontWeight: "600" }}>
                                        Current Step Progress:
                                    </span>
                                    <span style={{ float: "right", fontSize: "14px" }}>
                                        {progress.stepCount} / {progress.stepTotal} (
                                        {stepProgressPercentage}%)
                                    </span>
                                </div>
                                <div
                                    style={{
                                        width: "100%",
                                        backgroundColor: "#e0e0e0",
                                        borderRadius: "4px",
                                        overflow: "hidden",
                                        height: "16px"
                                    }}>
                                    <div
                                        style={{
                                            width: `${stepProgressPercentage}%`,
                                            height: "100%",
                                            backgroundColor: "#28a745",
                                            transition: "width 0.3s ease"
                                        }}
                                    />
                                </div>
                            </div>
                        </div>
                    )}
                </div>

                {/* Migration Backups DataTable */}
                <div>
                    <h3 style={{ marginBottom: "20px" }}>Migration Backups</h3>
                    <DataTable
                        isLoading={isLoading}
                        useInternalHeader
                        tableColumns={columns}
                        data={backups}
                        buttons={buttons}
                        pagingEnabled={true}
                        defaultSortFieldName="createOn"
                        defaultSortOrder="desc"
                    />
                </div>
            </div>
        </ContentBox>
    );
};
